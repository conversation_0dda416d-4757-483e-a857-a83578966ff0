import { createEffect, createMemo, createResource, createSignal, For, Show } from "solid-js";
import { fetchJson, FormField, modelX } from "./utils";
import type { VoiceInfo } from '../src/edge-tts'

const model = modelX
interface Resp {
  audio: string;
  wordSubtitle: string;
  sentenceSubtitle: string;
}

export function TTSPage() {
  const [text, setText] = createSignal('')
  const [resp, setResp] = createSignal<Resp | undefined>()
  const [loading, setLoading] = createSignal(false)

  const [audioUrl, setAudioUrl] = createSignal('')
  const [srt1Url, setSrt1Url] = createSignal('')
  const [srt2Url, setSrt2Url] = createSignal('')
  const [voice, setVoice] = createSignal('en-US-AndrewMultilingualNeural')
  const [voices] = createResource<VoiceInfo[]>(() => fetchJson('/tts/listVoices'))

  createEffect(() => {
    let url = audioUrl()
    if (!url) return
    return () => URL.revokeObjectURL(url)
  })

  const audioDownloadAPIUrl = createMemo(() => `/tts?${new URLSearchParams({
    voice: voice(),
    format: 'audio',
    text: text(),
  })}`)

  async function start() {
    try {
      setLoading(!0)

      const resp: Resp = await fetchJson('/tts', {
        query: {
          voice: voice(),
          text: text(),
          format: 'full'
        },
      })

      let rawStr = atob(resp.audio)
      let blob = new Blob([Uint8Array.from(rawStr, s => s.charCodeAt(0))], { type: 'audio/mp3' })
      setAudioUrl(URL.createObjectURL(blob))
      setResp(resp)

      let srt1 = new File([resp.sentenceSubtitle], 'srt1.srt', { type: 'text/plain' })
      setSrt1Url(URL.createObjectURL(srt1))

      let srt2 = new File([resp.sentenceSubtitle], 'srt2.srt', { type: 'text/plain' })
      setSrt1Url(URL.createObjectURL(srt2))
    } catch (error) {
      console.log("error", error)
      alert(String(error))
    } finally {
      setLoading(false)
    }
  }

  return <section class="section">
    <div class="container">
      <h1 class="title">TTS</h1>

      <FormField label="voice">
        <div class="select">
          <select use:model={[voice, setVoice]}>
            <For each={voices() || []}>{
              item => <option value={item.ShortName}>{item.Name.slice(1 + item.Name.indexOf('('), -1)}</option>
            }</For>
          </select>
        </div>
      </FormField>

      <FormField label="content">
        <textarea class="textarea" placeholder="Enter what you want to say" use:model={[text, setText]}></textarea>
      </FormField>

      <div class="field is-grouped">
        <div class="control">
          <button class="button is-primary" classList={{ 'is-loading': loading() }} disabled={loading()} onClick={start}>Start compositing</button>
        </div>
      </div>

      <Show when={audioUrl()}>
        <div class="block" style={{
          display: 'flex',
          'align-items': 'center',
          gap: '8px'
        }}>
          <audio autoplay src={audioUrl()} controls />
          <a class="button" download={"audio-" + text().slice(0, 10) + ".mp3"} href={audioUrl()}>Download audio file</a>
        </div>
      </Show>

      <Show when={resp()}>

        <FormField label="Subtitle 1">
          <a class="button" download={"audio-" + text().slice(0, 10) + ".srt"} href={srt1Url()}>Download srt subtitles</a>
          <textarea class="textarea" readOnly>{resp()!.sentenceSubtitle}</textarea>
        </FormField>

        <FormField label="Subtitle 2">
          <a class="button" download={"audio-" + text().slice(0, 10) + "-words.srt"} href={srt2Url()}>Download srt subtitles</a>
          <textarea class="textarea" readOnly>{resp()!.wordSubtitle}</textarea>
        </FormField>

      </Show>

      <a href={audioDownloadAPIUrl()} download="audio.mp3" class="button is-link">Download audio files (API)</a>
    </div>
  </section>
}