## Special reservations

### /to/:url

Returns the data of the corresponding address and modifies the CORS response header.

> _Special Function_ supports passing `__balanceExtract=xxx` parameter to extract JSON data in the document.
>
> - This parameter can be passed in multiple times to achieve progressive search
> - If one of them starts with `/` and ends with `/i` or `/`, it is considered a regular expression search location
>
> example:
>
> - Douban movie search: `/to/https://search.douban.com/movie/subject_search?search_text=%E6%9C%BA%E5%99%A8%E4%BA%BA&cat=1002&__balanceExtract=window.__DATA__`
> - Douban movie information: `/to/https://movie.douban.com/subject/4888853/?__balanceExtract=ld%2Bjson&__balanceExtract={`
